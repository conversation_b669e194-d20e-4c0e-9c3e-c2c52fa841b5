import GeneralNodes from "@/apps/Common/GeneralNotdes";
import { MazakaButton } from "@/apps/Common/MazakaButton";
import { MazakaForm } from "@/apps/Common/MazakaForm";
import { MazakaInput } from "@/apps/Common/MazakaInput";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import useMazakaForm from "@/hooks/useMazakaForm";
import { Col, Form, Modal, Row, Typography } from "antd";
import { FC, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useParams, useSearchParams } from "react-router-dom";
import endPoints from "../../../EndPoints";
import { useQueryClient } from "react-query";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import { connectEdge, deleteEdge, updateEdgeWithPut } from "../../../Services";
import TransationIndex from "../TransationIndex";
import ListItems from "../Rules/ListItems";

const AddOrUpdateEdge: FC<{ selectedRecord?: any; onFinish: any,setSelectedRecord?:any }> = ({
  selectedRecord,
  setSelectedRecord,
  onFinish,
}) => {
  const [form] = Form.useForm();
  const [searchParams, setSearchParams] = useSearchParams();
  const { formActions, mazakaForm } = useMazakaForm(form);
  const [selectedFromNodeId, setSelectedFromNodeId] = useState<null | string>(
    null
  );
  const {Text} = Typography
  const [selectedToNodeId, setSelectedToNodeId] = useState<null | string>(null);
  const { t } = useTranslation();
  const { workFlowId } = useParams();
  const queryClient = useQueryClient();
  const handleOnFinish = async (values:any,type:string="save") => {
   
    mazakaForm.setLoading();
    const formValues = form.getFieldsValue();

    try {
      formValues["FlowId"] = workFlowId;
      if (selectedRecord && selectedRecord?.type !== "connect") {
        formValues["Id"] = selectedRecord?.id;
        await updateEdgeWithPut(formValues);
      } else {
        if(type==="save")
        {

          await connectEdge(formValues);
        }
        else{
          const response =  await connectEdge(formValues);
          setSelectedRecord(
            {
              source: formValues["FromNodeId"],
              target:  formValues["ToNodeId"],
              savedEdgeName:  formValues["Name"],
              id:response?.Value
            }
          )

        }
      }
      mazakaForm.setSuccess(2000, () => t("form.transactionSuccessful"));
      openNotificationWithIcon("success", t("form.transactionSuccessful"));
      form.resetFields();

      queryClient.resetQueries({
        queryKey: endPoints.getNodeEdgeList,
        exact: false,
      });
      if(type==="save")
      {
        onFinish();
      
      }
     
    } catch (error) {
      showErrorCatching(error, mazakaForm, true, t);
    }
  };

  useEffect(() => {
    if (selectedRecord) {
      
      form.setFieldsValue({
        FromNodeId: selectedRecord?.source,
        ToNodeId: selectedRecord?.target,
        Name:selectedRecord?.savedEdgeName|| selectedRecord?.label||`${selectedRecord?.sourceNode?.data?.label||""}-${selectedRecord?.targetNode?.data?.label||""}`,
      });
    }
  }, [selectedRecord]);

  const deleteEdgeConfirm = () => {
    Modal.confirm({
      title: t("profession.warning"),
      icon: null,
      content: t("profession.deleteModalDesc"),
      okText: t("profession.delete"),
      cancelText: t("profession.cancel"),
      onOk: async () => {
        try {
          await deleteEdge({ ...selectedRecord, Id: selectedRecord?.id });
          openNotificationWithIcon("success", t("form.transactionSuccessful"));
          queryClient.resetQueries({
            queryKey: endPoints.getNodeEdgeList,
            exact: false,
          });
          form.resetFields();
          onFinish();
        } catch (error: any) {
          showErrorCatching(error, null, false, t);
        }
      },
    });
  };

  useEffect(() => {
    if (selectedRecord?.id) {
      const newParams = new URLSearchParams(searchParams.toString());
      newParams.set("edgeId", selectedRecord.id);
      setSearchParams(newParams);
    }
  }, [selectedRecord]);

 

  return (
    <>
      <Col xs={24}>
        <MazakaForm
          form={form}
          onFinish={handleOnFinish}
          submitButtonVisible={false}
        >
          <Row gutter={[10, 10]}>
            {
              selectedRecord&&
            <MazakaInput
              xs={24}
              xl={8}
              size="large"
              label={t("workFlow.name")}
              placeholder={t("workFlow.name")}
              name={"Name"}
              rules={[{ required: true, message: "" }]}
            />
            }
            <GeneralNodes
              xs={24}
              xl={8}
              label={t("workFlow.fromStep")}
              placeholder={t("workFlow.fromStep")}
              name={"FromNodeId"}
              rules={[{ required: true, message: "" }]}
              externalValueId={workFlowId}
              onChange={(value: string,obj:any) => {
                setSelectedFromNodeId(value);
                let currentName = form.getFieldValue("Name")
                if(currentName?.includes("-"))
                {
                  let splitName = currentName?.split("-")
                  form.setFieldValue("Name",`${obj?.label||""}-${splitName[1]}`)
                }
                else{

                  form.setFieldValue("Name",obj?.label||""+"-")
                }
              }}
              excludeIds={[selectedToNodeId]}
              allowClear
            />
            <GeneralNodes
            xl={8}
              allowClear
              label={t("workFlow.toStep")}
              placeholder={t("workFlow.toStep")}
              xs={24}
              name={"ToNodeId"}
              rules={[{ required: true, message: "" }]}
              externalValueId={workFlowId}
              excludeIds={[selectedFromNodeId]}
              
              onChange={(value: string,obj:any) => {
                setSelectedToNodeId(value);
                let currentName = form.getFieldValue("Name")
                if(currentName?.includes("-"))
                {
                  let splitName = currentName?.split("-")
                  form.setFieldValue("Name",`${splitName[0]}-${obj?.label||""}`)
                }
                else{

                  form.setFieldValue("Name","-"+obj?.label||"")
                }
              }}
            />

{
              !selectedRecord&&
            <MazakaInput
              xs={24}
              label={t("workFlow.name")}
              placeholder={t("workFlow.name")}
              name={"Name"}
              rules={[{ required: true, message: "" }]}
            />
            }

            <Col xs={24} className="!flex gap-1">
              <MazakaButton
                htmlType="submit"
                processType={formActions.submitProcessType}
                status="save"
              >
                {t("workFlow.save")}
              </MazakaButton>

              {selectedRecord && selectedRecord?.type !== "connect" ? (
                <MazakaButton
                  htmlType="button"
                  status="error"
                  onClick={deleteEdgeConfirm}
                >
                  {t("workFlow.deleteTransition")}
                </MazakaButton>
              ):
              <>
              <MazakaButton
                  htmlType="button"
                  status="save"
                  onClick={async()=>{
                     await form.validateFields();
              
                    handleOnFinish("save&AddRule")
                  }}
                >
                  {t("workFlow.saveAndAddRule")}
                </MazakaButton>
              
              </>
              }
            </Col>
            {selectedRecord && selectedRecord?.type !== "connect" && (
              <Col xs={24} className="!mt-4">
                <Row gutter={[0, 15]}>
                  
                  <Col xs={24}>
                    <TransationIndex />
                  </Col>
                  {
                    searchParams.get("edgeId")&&
                  <Col xs={24}>
                    <ListItems />
                  </Col>
                  }
                </Row>
              </Col>
            )}
          </Row>
        </MazakaForm>
      </Col>
    </>
  );
};

export default AddOrUpdateEdge;

// DynamicForm.tsx
import { MazakaButton } from "@/apps/Common/MazakaButton";
import { MazakaForm } from "@/apps/Common/MazakaForm";
import CreateFormItem from "@/apps/Form/CreateFormItem";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import useMazakaForm from "@/hooks/useMazakaForm";
import { Col, Form, Row } from "antd";
import { FC, Fragment, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useQueryClient } from "react-query";
import endPoints from "../../../EndPoints";
import { createEdgeRule, updateEdgeRuleWithPut } from "../../../Services";
import { useSearchParams } from "react-router-dom";
import { useGetEdgeRules, useGetEdgeRuleTypes } from "../../../ServerSideStates";

const DynamicForm: FC<{ selectedRecord: any; onFinish: any,mode:"add"|"edit", }> = ({
  selectedRecord,
  onFinish,
  mode,
}) => {
  const [form] = Form.useForm();
  const { formActions, mazakaForm } = useMazakaForm(form);
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();

  const rules = useGetEdgeRules({
    PageNumber: 1,
    PageSize: 100,
    transitionId: searchParams.get("edgeId"),

  });

  const watchedValues = Form.useWatch([], form);
 const ruleTypes = useGetEdgeRuleTypes();
   const handleFormContent = ()=>{
    if(!selectedRecord)
    {
      return {}
    }
    let allRuleTypes = ruleTypes.data?.Value

   
     const selectRuleType = allRuleTypes?.find((item:any)=>item.Id===selectedRecord?.RuleType)
   
    return JSON.parse(selectRuleType?.FormConfigurationJson
      ||'{}')?.fields||[]
   
  }

  console.log("selected rules",selectedRecord)
  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    const formValues = form.getFieldsValue();
    
    try {
      let data = {
        Name: selectedRecord?.Name,
        Description: selectedRecord?.Description,
        RuleType: selectedRecord?.Id,
        Order: rules?.data?.Count ? rules?.data?.Count + 1 : 1,
        IsActive: true,
        Configuration: formValues ? JSON.stringify(formValues) : "{}",
      };
      if(selectedRecord?.Id&& typeof selectedRecord?.Id==="number")
      {

        await createEdgeRule(searchParams.get("edgeId") || "", data);
      }
      else{
        let data = {...selectedRecord}
        data["Configuration"]=formValues ? JSON.stringify(formValues) : '{}'
        await updateEdgeRuleWithPut(data)
      }
      mazakaForm.setSuccess(2000, () => t("form.transactionSuccessful"));
      openNotificationWithIcon("success", t("form.transactionSuccessful"));
      form.resetFields();

      queryClient.resetQueries({ queryKey: endPoints.getEdgeRulesList });
      queryClient.resetQueries({ queryKey: endPoints.getNodeEdgeList });
      onFinish();
    } catch (error) {
      showErrorCatching(error, mazakaForm, true, t);
    }
  };

  const formData =  mode==="add"&& selectedRecord.FormConfigurationJson?JSON.parse(selectedRecord.FormConfigurationJson).fields:handleFormContent()

  useEffect(()=>{
    if(mode==="edit")
    {
      
      const initialData = selectedRecord?.Configuration?JSON.parse( selectedRecord?.Configuration):{}
      console.log(initialData)
      form.setFieldsValue({...initialData})
    }

  },[mode,selectedRecord])



  return (
    <Col xs={24}>
      <MazakaForm
        form={form}
        onFinish={handleOnFinish}
        submitButtonVisible={false}
      >
        <Row gutter={[0, 10]}>
          {
        formData?.map(
                (formItem: any, index: number) => (
                  <Fragment key={index}>
                    <CreateFormItem item={formItem} xs={24} watchedValues={watchedValues} form={form} />
                  </Fragment>
                )
              )
            ||[]}
          <Col xs={24}>
            <MazakaButton
              htmlType="submit"
              processType={formActions.submitProcessType}
              status="save"
            >
              {t("workFlow.save")}
            </MazakaButton>
          </Col>
        </Row>
      </MazakaForm>
    </Col>
  );
};

export default DynamicForm;
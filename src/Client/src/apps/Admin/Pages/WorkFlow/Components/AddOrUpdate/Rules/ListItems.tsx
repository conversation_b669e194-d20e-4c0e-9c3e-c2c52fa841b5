import React, { FC, useContext, useEffect, useMemo, useState } from "react";
import { Button, Col, Modal, Table, Tooltip, Typography } from "antd";
import { DeleteOutlined, FormOutlined, HolderOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { useQueryClient } from "react-query";
import { useGetEdgeRules } from "../../../ServerSideStates";
import { bulkUpdateRuleOrders, deleteEdgeRule } from "../../../Services";
import endPoints from "../../../EndPoints";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import ExpandableText from "@/apps/Common/TruncatedDesc";
import {
  DndContext,
  DragEndEvent,
} from "@dnd-kit/core";
import {
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
  arrayMove,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import { useSearchParams } from "react-router-dom";
import RulesIndex from "./RulesIndex";

// ----------------------------------

const ListItems = () => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const { Text } = Typography;
 const [searchParams] = useSearchParams()
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [selectedRecord, setSelectedRecord] = useState<any | null>(null);
  const [isShowEditRuleModal, setIsShowEditModal] = useState(false);

  const [filter, setFilter] = useState({
    PageNumber: 1,
    PageSize: 10,
    transitionId: searchParams.get("edgeId")
  });

  const rules = useGetEdgeRules(filter);

  useEffect(() => {
    if (rules?.data?.Value) {
      const enriched = rules.data.Value.map((item: any) => ({
        ...item,
        key: item.Id, // drag için key gerekiyor
      }));
      setDataSource(enriched);
    }
  }, [rules.data]);

  const handleChangePagination = (pageNum: number, pageSize: number) => {
    setFilter({ ...filter, PageNumber: pageNum, PageSize: pageSize });
  };

  const confirmDelete = (record: any) => {
    Modal.confirm({
      title: t("profession.warning"),
      content: t("profession.deleteModalDesc"),
      okText: t("profession.delete"),
      cancelText: t("profession.cancel"),
      icon:false,
      onOk: async () => {
        try {
          await deleteEdgeRule(record);
          openNotificationWithIcon("success", t("form.transactionSuccessful"));
          queryClient.resetQueries({ queryKey: endPoints.getEdgeRulesList });
          queryClient.resetQueries({ queryKey: endPoints.getNodeEdgeList });
        } catch (error: any) {
          showErrorCatching(error, null, false, t);
        }
      },
    });
  };

  const RowContext = React.createContext<any>({});

  const DragHandle: React.FC = () => {
    const { setActivatorNodeRef, listeners } = useContext(RowContext);
    return (
      <Button
        type="text"
        size="small"
        icon={<HolderOutlined />}
        style={{ cursor: "move" }}
        ref={setActivatorNodeRef}
        {...listeners}
      />
    );
  };

  interface RowProps extends React.HTMLAttributes<HTMLTableRowElement> {
    "data-row-key": string;
  }

  const Row: React.FC<RowProps> = (props) => {
    const {
      attributes,
      listeners,
      setNodeRef,
      setActivatorNodeRef,
      transform,
      transition,
      isDragging,
    } = useSortable({ id: props["data-row-key"] });

    const style: React.CSSProperties = {
      ...props.style,
      transform: CSS.Translate.toString(transform),
      transition,
      ...(isDragging ? { position: "relative", zIndex: 9999 } : {}),
    };

    const contextValue = useMemo(
      () => ({ setActivatorNodeRef, listeners }),
      [setActivatorNodeRef, listeners]
    );

    return (
      <RowContext.Provider value={contextValue}>
        <tr ref={setNodeRef} style={style} {...attributes} {...props} />
      </RowContext.Provider>
    );
  };

  const onDragEnd = async({ active, over }: DragEndEvent) => {
    if (active.id !== over?.id) {
      const oldIndex = dataSource.findIndex((item) => item.key === active.id);
      const newIndex = dataSource.findIndex((item) => item.key === over?.id);
  
      let allData = arrayMove([...dataSource], oldIndex, newIndex);
      allData = allData.map((item, index) => ({
        ...item,
        Order: index + 1,
      }));
      let serverData = allData?.map((item:any)=>{
        return {
          TransitionRuleId:item.Id,
          Order:item.Order,
        }
      })
  
  

      try {
        await bulkUpdateRuleOrders({Rules:serverData})
        setDataSource(allData)
      } catch (error) {
        showErrorCatching(error,null,false,t)
      }
      // setDataSource(allData); // Eğer UI'yi güncellemek istersen tekrar aktif et
    }
  };

  const columns = [
    {
      title:"",
      // key: "sort",
      // align: "center",
      // width: 80,
      render: () => <DragHandle />,
    },
    {
      title: t("workFlow.name"),
      dataIndex: "Name",
      render: (value: string) => <Text className="!text-xs">{value}</Text>,
      width: "30%",
    },
    {
      title: t("workFlow.description"),
      dataIndex: "Description",
      render: (value: string) => (
        <ExpandableText title="" limit={100} text={value || ""} />
      ),
      width: "60%",
    },
    {
      title: "",
      dataIndex: "edit",
      key: "edit",
      width: "10%",
      render: (_: any, record: any) => (
        <Col className="!flex gap-2 justify-end !px-2">
          <Tooltip title={t("users.list.edit")}>
            <FormOutlined
              className="!text-[#0096d1] !text-sm"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                setSelectedRecord(record);
                setIsShowEditModal(true);
              }}
            />
          </Tooltip>
          <Tooltip title={t("users.list.delete")}>
            <DeleteOutlined
              className="!text-[#9da3af] !text-sm"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                confirmDelete(record);
              }}
            />
          </Tooltip>
        </Col>
      ),
    },
  ];

  return (
    <>
      <DndContext modifiers={[restrictToVerticalAxis]} onDragEnd={onDragEnd}>
        <SortableContext
          items={dataSource.map((item) => item.key)}
          strategy={verticalListSortingStrategy}
        >
          <Table
            components={{
              body: {
                row: Row,
              },
            }}
            columns={columns}
            dataSource={dataSource?.sort((a,b)=>a?.Order-b?.Order)}
            loading={rules.isLoading || rules.isFetching}
            rowKey="Id"
            pagination={{
              position: ["bottomRight"],
              className: "!px-0",
              onChange: handleChangePagination,
              total: rules.data?.FilteredCount || 0,
              current: rules.data?.PageNumber,
              pageSize: rules.data?.PageSize,
              showLessItems: true,
              size: "small",
              showSizeChanger: true,
              locale: { items_per_page: "" },
              showTotal: (total) => `${total}`,
            }}
            onRow={(record) => ({
              onClick: () => {
                setSelectedRecord(record);
                setIsShowEditModal(true);
              },
            })}
          />
        </SortableContext>
      </DndContext>

      {/* Modal (isteğe bağlı aktif et) */}
      <Modal
        open={isShowEditRuleModal}
        onCancel={() => setIsShowEditModal(false)}
        footer={false}
        title=""
      >
        <RulesIndex
        mode="edit"
          selectedRecord={selectedRecord}
          setSelectedRecord={setSelectedRecord}
          onFinish={() => setIsShowEditModal(false)}
        />
      </Modal>
    </>
  );
};

export default ListItems;

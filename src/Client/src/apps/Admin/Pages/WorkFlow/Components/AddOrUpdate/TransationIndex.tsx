import { PlusOutlined } from "@ant-design/icons";
import { Col, Modal, Row, Typography } from "antd";
import { useState } from "react";
import RulesIndex from "./Rules/RulesIndex";
import { useTranslation } from "react-i18next";
import { <PERSON><PERSON><PERSON>Button } from "@/apps/Common/MazakaButton";

const TransationIndex = () => {
  const [selectedRecord, setSelectedRecord] = useState<any | null>(null);
  const { Text } = Typography;
  const [isShowRuleModal, setIsShowRuleModal] = useState(false);
  const { t } = useTranslation();
  return (
    <>
      <Col xs={24}>
        <Row gutter={[0, 10]}>
          <Col xs={24} className="!flex justify-end  ">
            <MazakaButton
              onClick={() => {
                setIsShowRuleModal(true);
              }}
              icon={<PlusOutlined />}
            >
              {t("workFlow.addRule")}
            </Ma<PERSON>aButton>
          </Col>
        </Row>
      </Col>
      <Modal
        open={isShowRuleModal}
        onCancel={() => {
          setIsShowRuleModal(false);
          setSelectedRecord(null)
        }}
        footer={false}
        title={""}
      >
        <RulesIndex
        mode="add"
          setSelectedRecord={setSelectedRecord}
          selectedRecord={selectedRecord}
          onFinish={() => {
            
            setIsShowRuleModal(false);
            setSelectedRecord(null)
          }}
        />
      </Modal>
    </>
  );
};

export default TransationIndex;

import { BulbFilled, } from "@ant-design/icons";
import {
    BaseEdge,
    EdgeLabelRenderer,
    getSmoothStepPath,
  } from "@xyflow/react";
  
  export default function CustomEdge({ id, sourceX, sourceY, targetX, targetY, sourcePosition, targetPosition,data}) {
  
    // Çizgi tipini smoothstep yap
    const [edgePath, labelX, labelY] = getSmoothStepPath({
      sourceX,
      sourceY,
      sourcePosition,
      targetX,
      targetY,
      targetPosition,
     
    });


  
    return (
      <>
        <BaseEdge id={id} path={edgePath} />
        <EdgeLabelRenderer>
          <div 
          
            style={{
              position: "absolute",
              transform: `translate(-50%, -50%) translate(${labelX}px, ${labelY}px)`,
              pointerEvents: "all",
              background: "#fff",
              border: "1px solid #ccc",
              borderRadius: "4px",
           
             
            
            }}
            className="nodrag nopan !flex gap-1 items-center !p-1 cursor-pointer !text-xs !bg-[#35b214] !text-white"
            
          >
            {
                data?.ruleCount>0&& <BulbFilled className="!text-yellow-400 !text-base"/>
            }
           
            {data?.name||""}
          </div>
        </EdgeLabelRenderer>
      </>
    );
  }
  
import { DataResponse } from "@/services/BaseResponseModel";
import endpoints from "./EndPoints";
import headers from "@/services/Headers.json";
import { deleteRequest, get, post, put } from "@/services/Client";
import { CreateUrlFilter } from "@/helpers/CreateURLFilter";

export const getWorkFlowListFilter = async (
  inputFilter: any
): Promise<DataResponse<any>> => {
  const query = inputFilter ? CreateUrlFilter(inputFilter) : null;

  const url = `${endpoints.getWorkFlowListFilter}?${query || ""}`;

  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};

export const getWorkFlowNodeListFilter = async (
  inputFilter: any
): Promise<DataResponse<any>> => {
  const query = inputFilter ? CreateUrlFilter(inputFilter) : null;

  const url = `${endpoints.getWorkFlowNodes}?${query || ""}`;

  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};

export const getNodeEdgeListFilter= async (
  inputFilter: any
): Promise<DataResponse<any>> => {
  const query = inputFilter ? CreateUrlFilter(inputFilter) : null;

  const url = `${endpoints.getNodeEdgeList}?${query || ""}`;

  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};

export const getEdgeRuleListFilter= async (
  inputFilter: any
): Promise<DataResponse<any>> => {
  const query = inputFilter ? CreateUrlFilter(inputFilter) : null;

  const url = `${endpoints.getEdgeRulesList}?${query || ""}`;

  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};

export const getEdgeRuleTypeList = async (): Promise<DataResponse<any>> => {
  const url = `${endpoints.getRuleTypeList}`;
  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};

export const getWorkFlowDetails = async (WorkFlowId:string): Promise<DataResponse<any>> => {
  const url = `${endpoints.createWorkFlow}/${WorkFlowId}`;
  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};

export const createWorkFlow = async (data: any): Promise<DataResponse<any>> => {
  const url = `${endpoints.createWorkFlow}`;
  const config = headers.content_type.application_json;
  return post<DataResponse<any>>(url, data, config);
};

export const addNodeToWorkFlow = async (data: any): Promise<DataResponse<any>> => {
  const url = `${endpoints.addNodeToWorkFlow}`;
  const config = headers.content_type.application_json;
  return post<DataResponse<any>>(url, data, config);
};


export const connectEdge = async (data: any): Promise<DataResponse<any>> => {
  const url = `${endpoints.connectEdge}`;
  const config = headers.content_type.application_json;
  return post<DataResponse<any>>(url, data, config);
};
export const updateNodeOnWorkFlow= async (data: any): Promise<DataResponse<any>> => {
  const url = `${endpoints.updateNodeOnWorkFlow}/${data.Id}`;
  const config = headers.content_type.application_json;
  return put<DataResponse<any>>(url, data, config);
};

export const updateEdgeWithPut= async (data: any): Promise<DataResponse<any>> => {
  const url = `${endpoints.editEdge}/${data.Id}`;
  const config = headers.content_type.application_json;
  return put<DataResponse<any>>(url, data, config);
};
export const copyWorkFlow = async (WorkFlow:any): Promise<DataResponse<any>> => {
  const url = `${endpoints.copyWorkFlow}/${WorkFlow?.Id}/copy`;
  const config = headers.content_type.application_json;
  return post<DataResponse<any>>(url,WorkFlow, config);
};

export const createEdgeRule = async (edgeId:string,data:any): Promise<DataResponse<any>> => {
  const url = `${endpoints.createEdgeRule}/${edgeId}/rules`;
  const config = headers.content_type.application_json;
  return post<DataResponse<any>>(url,data, config);
};

export const updateEdgeRuleWithPut= async (data: any): Promise<DataResponse<any>> => {
  const url = `${endpoints.editEdgeRule}/${data.Id}`;
  const config = headers.content_type.application_json;
  return put<DataResponse<any>>(url, data, config);
};


export const bulkUpdateRuleOrders= async (data: any): Promise<DataResponse<any>> => {
  const url = `${endpoints.bulkUpdateOrder}`;
  const config = headers.content_type.application_json;
  return put<DataResponse<any>>(url, data, config);
};

export const updateWorkFlowWithPut = async (
  data: any
): Promise<DataResponse<any>> => {
  const url = `${endpoints.updateWorkFlowWithUrl}/${data.Id}`;
  const config = headers.content_type.application_json;
  return put<DataResponse<any>>(url, data, config);
};

export const deleteWorkFlow = async (data: any): Promise<DataResponse<any>> => {
  const url = `${endpoints.deleteWorkFlow}/${data.Id}`;
  const config = headers.content_type.application_json;
  return deleteRequest<DataResponse<any>>(url, data, config);
};

export const deleteEdge = async (data: any): Promise<DataResponse<any>> => {
  const url = `${endpoints.removeEdge}/${data.Id}`;
  const config = headers.content_type.application_json;
  return deleteRequest<DataResponse<any>>(url, data, config);
};
export const deleteEdgeRule = async (data: any): Promise<DataResponse<any>> => {
  const url = `${endpoints.editEdgeRule}/${data.Id}`;
  const config = headers.content_type.application_json;
  return deleteRequest<DataResponse<any>>(url, data, config);
};

export const deleteNode = async (data: any): Promise<DataResponse<any>> => {
  const url = `${endpoints.deleteNode}/${data.Id}`;
  const config = headers.content_type.application_json;
  return deleteRequest<DataResponse<any>>(url, data, config);
};

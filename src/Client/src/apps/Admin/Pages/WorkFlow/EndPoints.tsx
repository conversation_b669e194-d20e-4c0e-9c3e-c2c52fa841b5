const baseRequestUrl = "requests"
  const endpoints = {
   getWorkFlowListFilter:`${baseRequestUrl}/Flows`,
   getWorkFlowDetails:`${baseRequestUrl}/Flows`,
   createWorkFlow:`${baseRequestUrl}/Flows`,
   copyWorkFlow:`${baseRequestUrl}/Flows`,
   updateWorkFlowWithUrl:`${baseRequestUrl}/Flows`,
   deleteWorkFlow:`${baseRequestUrl}/Flows`,

   getWorkFlowNodes:`${baseRequestUrl}/nodes`,
   addNodeToWorkFlow:`${baseRequestUrl}/nodes`,
   updateNodeOnWorkFlow:`${baseRequestUrl}/nodes`,
   deleteNode:`${baseRequestUrl}/nodes`,
   
   getNodeEdgeList:`${baseRequestUrl}/transitions`,
   connectEdge:`${baseRequestUrl}/transitions`,
   editEdge:`${baseRequestUrl}/transitions`,
   removeEdge:`${baseRequestUrl}/transitions`,

   getEdgeRulesList:`${baseRequestUrl}/rules`,
   getRuleTypeList:`${baseRequestUrl}/rule-types`,
   createEdgeRule:`${baseRequestUrl}/transitions`,
   editEdgeRule:`${baseRequestUrl}/rules`,
   bulkUpdateOrder:`${baseRequestUrl}/rules/bulk-update-orders`,
   deleteEdgeRule:`${baseRequestUrl}/rules`,
  };
  
  export default endpoints;
  
﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Requests.Infrastructure.Data.Migrations
{
    /// <inheritdoc />
    public partial class RequestsData011 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "ChangedByUserId",
                schema: "Requests",
                table: "TicketHistories",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ChangedByUserName",
                schema: "Requests",
                table: "TicketHistories",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_TicketHistories_ChangedByUserId",
                schema: "Requests",
                table: "TicketHistories",
                column: "ChangedByUserId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_TicketHistories_ChangedByUserId",
                schema: "Requests",
                table: "TicketHistories");

            migrationBuilder.DropColumn(
                name: "ChangedByUserId",
                schema: "Requests",
                table: "TicketHistories");

            migrationBuilder.DropColumn(
                name: "ChangedByUserName",
                schema: "Requests",
                table: "TicketHistories");
        }
    }
}

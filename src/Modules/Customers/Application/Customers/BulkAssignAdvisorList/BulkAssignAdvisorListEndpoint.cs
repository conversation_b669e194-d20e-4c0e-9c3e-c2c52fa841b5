using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Application;
using Shared.Endpoints;

namespace Customers.Application.Customers.BulkAssignAdvisorList;

internal sealed class BulkAssignAdvisorListEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("/api/v1/customers/management/bulk-assign-advisor-list", async (
            BulkAssignAdvisorListRequest request,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var command = new BulkAssignAdvisorListCommand(request.CustomerIds, request.AdvisorIds);
            var result = await mediator.Send(command, cancellationToken);
            return result.Match(Results.NoContent, CustomResults.Problem);
        })
        .WithTags("Customers.Management")
        .WithGroupName("apiv1")
        .RequireAuthorization("Customers.Management")
        .Produces<Result>(StatusCodes.Status204NoContent)
        .ProducesProblem(StatusCodes.Status400BadRequest);
    }
}

public record BulkAssignAdvisorListRequest(
    Guid[] CustomerIds,
    Guid[] AdvisorIds
);
